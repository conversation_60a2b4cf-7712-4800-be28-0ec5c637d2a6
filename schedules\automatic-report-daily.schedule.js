const { reportSubscriptionData } = require('./report-subscription.schedule');
const { rrulestr } = require('rrule');
const { createReportLog } = require('../api/helpers/report-log');
const { getOrCreateQueue, getOrCreate<PERSON><PERSON>ker, has<PERSON><PERSON><PERSON>, initializeWorkerManager } = require('../config/worker-manager');

// Global variables to track instances and prevent duplicates
let isScheduleInitialized = false;

// this job will run every day midnight and create separate job for send report based on configuration

const settings = {
    repeatStrategy: (millis, opts) => {
        const currentDate =
            opts.startDate && new Date(opts.startDate) > new Date(millis)
                ? new Date(opts.startDate)
                : new Date(millis);
        const rrule = rrulestr(opts.pattern);
        if (rrule.origOptions.count && !rrule.origOptions.dtstart) {
            throw new Error('DTSTART must be defined to use COUNT with rrule');
        }

        const next_occurrence = rrule.after(currentDate, false);
        return next_occurrence?.getTime();
    },
};

const setUpReportSchedule = async () => {
    // Prevent duplicate initialization
    if (isScheduleInitialized) {
        console.log('🔄 Report schedule already initialized - skipping duplicate setup');
        return;
    }

    console.log('📊 Setting up REPORTS Worker - preparing automated report generation...');

    // Initialize worker manager
    initializeWorkerManager();

    //await createReportLog({ event: "RUN_CRON_AT_MID_NIGHT", description: "Setting up REPORTS Worker..." })

    // Check if worker already exists
    if (hasWorker('reportSchedular')) {
        console.log('✅ Report worker already exists - reusing existing instance');
        isScheduleInitialized = true;
        return;
    }

    // Create queue and worker using worker manager
    const reportQueue = getOrCreateQueue('reportSchedular', settings);
    const reportWorker = getOrCreateWorker(
        'reportSchedular',
        async (job) => {
            console.log(`📊 Starting report generation for job ${job.id}...`);
            try {
                // Update job progress
                await job.updateProgress(10);
                console.log("🔍 Finding all report subscriptions and creating individual jobs...");

                await job.updateProgress(50);
                const res = await reportSubscriptionData();

                await job.updateProgress(100);
                console.log(`✅ Report generation completed for job ${job.id}`);
                return res;
            } catch (err) {
                console.error(`❌ Report generation failed for job ${job.id}:`, err);
                // Re-throw the error so BullMQ can handle retries
                throw new Error(`Report generation failed: ${err.message}`);
            }
        },
        {
            concurrency: 1, // Process one report batch at a time to avoid conflicts
            // Longer lock duration for report generation
            lockDuration: 1800000, // 30 minutes
            lockRenewTime: 900000, // Renew every 15 minutes
            ...settings
        }
    );

    reportWorker.on("active", async (job) => {
        console.log(`🔄 Processing report job ${job.id} - generating automated reports...`);
        console.log(`📊 Report batch: ${job?.data?.index || 'N/A'} - starting report generation`);
        //await createReportLog({ event: "WORKER_ACTIVE", description: "Report worker started processing job" })
    });

    reportWorker.on("completed", async (job, returnValue) => {
        console.log(`✅ Report job ${job.id} completed successfully!`);
        console.log(`📈 Report batch: ${job?.data?.index || 'N/A'} - all reports generated and sent`);
        // await createReportLog({ event: "WORKER_COMPLETED", description: "Report worker completed job successfully" })
    });

    reportWorker.on("error", async (failedReason) => {
        console.error(`❌ Report job failed:`, failedReason);
        console.error(`🚨 Report generation encountered an error - check logs for details`);
        await createReportLog({ event: "WORKER_FAILED", data: failedReason, description: "Report worker failed to process job" })
    });

    const d = new Date();
    const repeat = {
        // cron: '0 */5 * * * *', //Run in every minute for debug in local system
        // cron: '0 10 0 * * *',
        cron: '0 0 8 * * *',
        offset: d.getTimezoneOffset(),
        tz: 'America/Atikokan',
        immediately: false,
    }; // run every day midnight 12:00 

    reportQueue.add(
        'reportDaily',
        { type: 'Report' },
        {
            repeat,
            attempts: 3, // retry 3 times if fails
            removeOnComplete: 10, // Keep last 10 completed jobs
            removeOnFail: 50, // Keep last 50 failed jobs for debugging
            backoff: {
                type: 'exponential',
                delay: 5000, // Start with 5 second delay
            },
            // Job timeout - reports can take a while to generate
            timeout: 1800000, // 30 minutes timeout for report generation
        },
    );

    // Mark as initialized
    isScheduleInitialized = true;
    console.log('🎉 Report schedule initialized successfully - daily reports will be generated at 8:00 AM EST!');
}

// Cleanup function for graceful shutdown
const cleanupReportSchedule = async () => {
    // Worker manager will handle cleanup of workers and queues
    isScheduleInitialized = false;
    console.log('Report schedule cleaned up');
};

module.exports = { setUpReportSchedule, cleanupReportSchedule };
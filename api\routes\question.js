const route = require("express").Router();
const mongoose = require("mongoose");
const { getQuestionSetup, getQuestionAsColumns, updateQuestionOrderManually, updateQuestionLabel, saveQuestion, duplicateQuestionLabel, getAccountQuestions, cloneAccountQuestions, removeArchivedQuestions, deleteQuestion } = require("../helpers/question");
const authWithRole = require("../middleware/auth-with-role");
const Question = mongoose.model("question");
const QuestionOrder = mongoose.model("questionOrder");

route.get("/", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const query = {
      active: true,
      facilityId: mongoose.Types.ObjectId(req.query.facilityId),
      forType: req.query.forType,
    };

    if (req.query.forType === "transfer") {
      query.forTransferType = req.query.forTransferType;
    }

    const questions = await Question.find(query);

    res.send(questions);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.post("/set", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const response = await saveQuestion(req);
    res.send(response);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.post("/duplicate-question-label", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const response = await duplicateQuestionLabel(req);
    res.send(response);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

route.get("/set", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await getQuestionSetup(
      req.query.forType,
      req.query.forTransferType,
      req.headers.accountid
    );
    res.send(qs);
  } catch (error) {
    console.log(error, "error question");
    res.status(500).send(error);
  }
});

route.get("/table-columns", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await getQuestionAsColumns(req.query, req.headers);
    res.send(qs);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/account/questions", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await getAccountQuestions(req);
    res.send(qs);
  } catch (error) {
    console.log(error, "error question");
    res.status(500).send(error);
  }
});

route.post("/clone-account-question", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const response = await cloneAccountQuestions(req);
    res.send(response);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

route.put("/set/update-question-label", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await updateQuestionLabel(req);
    res.send(qs);
  } catch (error) {
    console.log(error, 'error');

    res.status(500).send(error);
  }
});

route.put("/set/question", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const { body } = req;
    const question = await Question.findByIdAndUpdate(req.query.id, body, {
      new: true,
    });
    res.send(question);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.put("/set", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await QuestionOrder.findByIdAndUpdate(
      req.query.id,
      { $set: { order: req.body } },
      { new: true }
    );
    res.send(qs);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.delete("/set", authWithRole("manageQuestions"), async (req, res) => {
  try {
    return await deleteQuestion(req, res);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

route.delete("/remove-archived-questions/:id?", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await removeArchivedQuestions(req);
    res.send(qs);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

route.post("/sync-question-orders", authWithRole("manageQuestions"), async (req, res) => {
  try {
    let qs = await updateQuestionOrderManually(req.headers);
    res.send(qs);
  } catch (error) {
    res.status(500).send(error);
  }
});

// New endpoint for auto-fill functionality
route.get("/auto-fill-sources", authWithRole("manageQuestions"), async (req, res) => {
  try {
    const { getAutoFillSourceQuestions } = require("../helpers/question");
    const questions = await getAutoFillSourceQuestions(req);
    res.send(questions);
  } catch (error) {
    console.log(error, 'error fetching auto-fill source questions');
    res.status(500).send(error);
  }
});

module.exports = route;

const mongoose = require("mongoose");
const { processAlertReportType } = require("./alert.process");
const AlertReport = mongoose.model("alertReport");


const alertSubscriptionData = async () => {
    try {
        let alertReports = await AlertReport.find()
            .populate("userId")
            .populate("facilityId")
            .exec();
        
        if (alertReports && alertReports.length > 0) {
            for await (let alertReport of alertReports) {
                await processAlertReportType(alertReport, updateAlertReports);
                console.log(`Running code for ${alertReport?.type}`);
            };
            // Place your code here that you want to run for the specific type
        }
    } catch (error) {
        console.log(error, "Alert Reporter schedular errors inside function");
    }
}

async function updateAlertReports(alertReport, date, lastCheckDateField = "lastCheckDate", alertReportType) {
    const latestStringDate = date || null;

    // Find the existing document
    const alertReportSaved = await AlertReport.findOne({ _id: alertReport._id });

    if (alertReportSaved) {
        // Prepare dynamic fields
        const fieldsToUpdate = { [lastCheckDateField]: latestStringDate };

        if (lastCheckDateField === `lastEmailSendDate_${alertReportType}`) {
            fieldsToUpdate[`lastCheckDate_${alertReportType}`] = null;
        }

        // Set fields dynamically with `{ strict: false }`
        alertReportSaved.set(fieldsToUpdate, undefined, { strict: false });

        // Save and return the updated document
        await alertReportSaved.save();
        return alertReportSaved;
    }
}

module.exports = {
    alertSubscriptionData,
    updateAlertReports
}

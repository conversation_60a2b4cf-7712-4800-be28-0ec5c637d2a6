import CloseIcon from "../../../../../assets/svgs/close.svg";
import styles from "./AddPatient.module.scss";

const ValidationADT = ({ selectedItem, close, hideHeader, onConfirmFutureDate }) => {
    const { errors } = selectedItem;

    // Check if there are any future date errors that can be confirmed
    const futureDateErrors = errors?.filter(error =>
        error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const otherErrors = errors?.filter(error =>
        !error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const handleConfirmFutureDates = () => {
        if (onConfirmFutureDate) {
            onConfirmFutureDate();
        }
    };

    return (
        <div className={styles.validationADT}>
            <div className={styles.validationADTOverlay}></div>
            <div className={styles.validationADTContent}>
                <div className={`df aic ${styles.hdr}`}>
                    <p className={`df aic ffmm fs16`}>Validation Error</p>
                    <div className={`mla`}>
                        <div className={`df acc ${styles.closeBtn}`} onClick={() => close()}>
                            <CloseIcon />
                        </div>
                    </div>
                </div>
                <div className={`p-t-8 p-r-20 p-b-20 p-l-20 df ffc ${styles.stepThree}`}>
                    {!hideHeader && <b>Please fill following required fields.</b>}

                    {/* Regular errors */}
                    {otherErrors.length > 0 && (
                        <ui className={`m-t-10 ${styles.errorList}`}>
                            {otherErrors.map((item, index) => (
                                <li key={index}>
                                    <span>{item}</span>
                                </li>
                            ))}
                        </ui>
                    )}

                    {/* Future date errors with confirmation option */}
                    {futureDateErrors.length > 0 && (
                        <div style={{ marginTop: '15px', padding: '15px', backgroundColor: '#fff8e1', border: '1px solid #ffc107', borderRadius: '4px' }}>
                            <p style={{ margin: '0 0 10px 0', color: '#e65100', fontSize: '14px', fontWeight: '600' }}>
                                ⚠️ Some dates are in the future
                            </p>
                            <button
                                onClick={handleConfirmFutureDates}
                                style={{
                                    padding: '10px 20px',
                                    backgroundColor: '#28a745',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    fontSize: '14px',
                                    fontWeight: '600',
                                    width: '100%'
                                }}
                            >
                                ✓ Yes, Save Patient
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ValidationADT;

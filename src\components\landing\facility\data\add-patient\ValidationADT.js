import CloseIcon from "../../../../../assets/svgs/close.svg";
import styles from "./AddPatient.module.scss";

const ValidationADT = ({ selectedItem, close, hideHeader, onConfirmFutureDate }) => {
    const { errors } = selectedItem;

    // Check if there are any future date errors that can be confirmed
    const futureDateErrors = errors?.filter(error =>
        error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const otherErrors = errors?.filter(error =>
        !error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const handleConfirmFutureDates = () => {
        if (onConfirmFutureDate) {
            onConfirmFutureDate();
        }
    };

    return (
        <div className={styles.validationADT}>
            <div className={styles.validationADTOverlay}></div>
            <div className={styles.validationADTContent}>
                <div className={`df aic ${styles.hdr}`}>
                    <p className={`df aic ffmm fs16`}>Validation Error</p>
                    <div className={`mla`}>
                        <div className={`df acc ${styles.closeBtn}`} onClick={() => close()}>
                            <CloseIcon />
                        </div>
                    </div>
                </div>
                <div className={`p-t-8 p-r-20 p-b-20 p-l-20 df ffc ${styles.stepThree}`}>
                    {!hideHeader && <b>Please fill following required fields.</b>}

                    {/* Regular errors */}
                    {otherErrors.length > 0 && (
                        <ui className={`m-t-10 ${styles.errorList}`}>
                            {otherErrors.map((item, index) => (
                                <li key={index}>
                                    <span>{item}</span>
                                </li>
                            ))}
                        </ui>
                    )}

                    {/* Future date errors with confirmation option */}
                    {futureDateErrors.length > 0 && (
                        <div className="m-t-15">
                            <b style={{ color: '#ff9800' }}>Future Date Confirmations:</b>
                            <ui className={`m-t-10 ${styles.errorList}`}>
                                {futureDateErrors.map((item, index) => (
                                    <li key={index} style={{ color: '#ff9800' }}>
                                        <span>{item}</span>
                                    </li>
                                ))}
                            </ui>
                            <div className="m-t-15 df aic" style={{ gap: '10px' }}>
                                <button
                                    className="btn btn-primary"
                                    onClick={handleConfirmFutureDates}
                                    style={{
                                        padding: '8px 16px',
                                        backgroundColor: '#1976d2',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                    }}
                                >
                                    Confirm Future Dates & Continue
                                </button>
                                <span style={{ fontSize: '12px', color: '#666' }}>
                                    Click to confirm all future dates and proceed with submission
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ValidationADT;

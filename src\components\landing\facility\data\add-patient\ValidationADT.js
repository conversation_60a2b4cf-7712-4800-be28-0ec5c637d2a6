import CloseIcon from "../../../../../assets/svgs/close.svg";
import styles from "./AddPatient.module.scss";

const ValidationADT = ({ selectedItem, close, hideHeader, onConfirmFutureDate }) => {
    const { errors } = selectedItem;

    // Check if there are any future date errors that can be confirmed
    const futureDateErrors = errors?.filter(error =>
        error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const otherErrors = errors?.filter(error =>
        !error.includes('is a future date. Please confirm to proceed.')
    ) || [];

    const handleConfirmFutureDates = () => {
        if (onConfirmFutureDate) {
            onConfirmFutureDate();
        }
    };

    return (
        <div className={styles.validationADT}>
            <div className={styles.validationADTOverlay}></div>
            <div className={styles.validationADTContent}>
                <div className={`df aic ${styles.hdr}`}>
                    <p className={`df aic ffmm fs16`}>Validation Error</p>
                    <div className={`mla`}>
                        <div className={`df acc ${styles.closeBtn}`} onClick={() => close()}>
                            <CloseIcon />
                        </div>
                    </div>
                </div>
                <div className={`p-t-8 p-r-20 p-b-20 p-l-20 df ffc ${styles.stepThree}`}>
                    {!hideHeader && <b>Please fill following required fields.</b>}

                    {/* Regular errors */}
                    {otherErrors.length > 0 && (
                        <ui className={`m-t-10 ${styles.errorList}`}>
                            {otherErrors.map((item, index) => (
                                <li key={index}>
                                    <span>{item}</span>
                                </li>
                            ))}
                        </ui>
                    )}

                    {/* Future date errors with confirmation option */}
                    {futureDateErrors.length > 0 && (
                        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3e0', border: '1px solid #ff9800', borderRadius: '4px' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                                <span style={{ fontSize: '16px', marginRight: '8px' }}>⚠️</span>
                                <b style={{ color: '#e65100', fontSize: '14px' }}>Future Date Confirmation Required</b>
                            </div>
                            <ui className={`m-t-10 ${styles.errorList}`} style={{ marginBottom: '15px' }}>
                                {futureDateErrors.map((item, index) => (
                                    <li key={index} style={{ color: '#e65100', fontSize: '13px' }}>
                                        <span>{item}</span>
                                    </li>
                                ))}
                            </ui>
                            <div style={{ borderTop: '1px solid #ffcc02', paddingTop: '15px' }}>
                                {/* <p style={{ fontSize: '13px', color: '#e65100', marginBottom: '10px', fontWeight: '500' }}>
                                    The dates above are in the future. Click below to confirm these dates, then click Save to save the patient.
                                </p> */}
                                <button
                                    onClick={handleConfirmFutureDates}
                                    style={{
                                        padding: '12px 20px',
                                        backgroundColor: '#ff9800',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '6px',
                                        cursor: 'pointer',
                                        fontSize: '14px',
                                        fontWeight: '600',
                                        width: '100%',
                                        transition: 'all 0.2s',
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                    }}
                                    onMouseOver={(e) => {
                                        e.target.style.backgroundColor = '#f57c00';
                                        e.target.style.transform = 'translateY(-1px)';
                                        e.target.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                                    }}
                                    onMouseOut={(e) => {
                                        e.target.style.backgroundColor = '#ff9800';
                                        e.target.style.transform = 'translateY(0)';
                                        e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                                    }}
                                >
                                    ✓ Confirm Future Dates
                                </button>
                                <p style={{ fontSize: '11px', color: '#666', marginTop: '8px', textAlign: 'center', fontStyle: 'italic' }}>
                                    After confirming, this dialog will close. Click Save to save the patient.
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ValidationADT;

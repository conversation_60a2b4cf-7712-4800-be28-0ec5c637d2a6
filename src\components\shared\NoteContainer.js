import * as React from 'react';
import { Box } from '@mui/material';
import { useSelector } from 'react-redux';
import NotesList from './notes/NotesList';


export default function NoteContainer({ children, page, openFrom = "page" }) {
    const { isNotePopup, isNoteExpanded } = useSelector((state) => state.common);

    const [rightContentVisible, setRightContentVisible] = React.useState(false);
    const [rightContentWidth, setRightContentWidth] = React.useState(300); // Default width

    React.useEffect(() => { // Set the right content width based on the isNoteExpanded state
        setRightContentWidth(isNoteExpanded ? 500 : 300);
    }, [isNoteExpanded]);

    React.useEffect(() => {
        setRightContentVisible(isNotePopup);
        setRightContentWidth(300);
    }, [isNotePopup]);

    return (
        <>
            <Box
                display="flex"
                flexDirection="row"
                width="100%"
                height={openFrom === "dialog" ? "100%" : "100vh"}
                style={{
                    overflow: "hidden",
                    position: "relative",
                }}
            >
                <Box
                    flex="1"
                    style={{
                        transition: "margin-right 0.3s",
                        overflow: openFrom === "page" ? "auto" : "auto",
                        height: "100%",
                        marginRight: rightContentVisible ? rightContentWidth : 0,
                    }}
                >
                    {children}
                </Box>

                {rightContentVisible && (
                    <Box
                        style={{
                            position: openFrom === "page" ? "fixed" : "absolute",
                            top: openFrom === "page" ? "80px" : "0",
                            right: "0",
                            bottom: openFrom === "page" ? "0" : "auto",
                            height: openFrom === "dialog" ? "100%" : "calc(100vh - 80px)",
                            transition: "width 0.3s", // Smooth transition for width
                            width: rightContentWidth,
                            backgroundColor: "#fff",
                            overflow: "hidden",
                            flexShrink: 0, // Prevent shrinking
                            zIndex: openFrom === "page" ? 1200 : 10,
                            ...(openFrom === "dialog" && { borderRadius: "4px" }),
                            ...(openFrom === "page" && {
                                borderLeft: "1px solid #e0e0e0",
                                boxShadow: "-2px 0 8px rgba(0,0,0,0.1)"
                            }),
                        }}
                        sx={{
                            ...(openFrom === "dialog" && { boxShadow: 3 }),
                        }}
                    >
                        <Box
                            padding={2}
                            height="100%"
                            display="flex"
                            flexDirection="column"
                            sx={{
                                overflowY: "hidden", // Prevent outer container from scrolling
                            }}
                        >
                            <NotesList
                                page={page}
                                openFrom={openFrom}
                            />
                        </Box>
                    </Box>
                )}
            </Box>
        </>
    )
}

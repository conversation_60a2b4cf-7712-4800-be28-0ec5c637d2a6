const QUESTION_INPUT_TYPE = {
    UNLIMITED_ANSWERS: 'unlimitedAnswers',
    LIMITED_ANSWERS: 'limitedAnswers',
    NUMBER_RANGE: 'numberRange',
    NUMBER_RANGE_LIMITED_ANSWERS: 'numberRangeLimitedAnswers',
    RESIDENCE_LIST: 'residenceList',
    DATE: 'date',
    TIME_TAB_RESIDENT_LIST: 'timeTabResidentList',
    TIME_TAB_RANGE: 'timeTabRange',
}


const QUESTIONS_TEMPLATE_TYPE = {
    DX_CARD: "dxCard",
    DAY_CARD: "dayCard",
    ANALYSIS: "analysis",
    USER_LIST: "userList",
    FLOOR_CARD: "floorCard",
}

const QUESTION_TYPE_OPTIONS = [
    { value: QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS, label: 'Unlimited answers' },
    { value: QUESTION_INPUT_TYPE.LIMITED_ANSWERS, label: 'Limited Answers' },
    { value: QUESTION_INPUT_TYPE.NUMBER_RANGE, label: 'Number range' },
    { value: QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS, label: 'Number range / limited answers' },
    { value: QUESTION_INPUT_TYPE.RESIDENCE_LIST, label: 'Residence list' },
    { value: QUESTION_INPUT_TYPE.DATE, label: 'Date' },
    { value: QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST, label: 'Time Tab - Resident List Only' },
    { value: QUESTION_INPUT_TYPE.TIME_TAB_RANGE, label: 'Time Tab - Time Range' },
]

const TIME_TAB_MODE = {
    RESIDENT_LIST_ONLY: 'residentListOnly',
    TIME_RANGE: 'timeRange',
}

const TIME_RANGE_TYPE = {
    HOURLY: 'hourly',
    CUSTOM: 'custom',
}

const DEFAULT_TIME_RANGES = [
    { label: 'Morning', startTime: '06:00', endTime: '12:00' },
    { label: 'Afternoon', startTime: '12:00', endTime: '18:00' },
    { label: 'Evening', startTime: '18:00', endTime: '00:00' },
    { label: 'Night', startTime: '00:00', endTime: '06:00' },
];

const HOURLY_TIME_RANGES = [
    { label: '12am to 12:59am', value: '00:00', startTime: '00:00', endTime: '00:59' },
    { label: '1am to 1:59am', value: '01:00', startTime: '01:00', endTime: '01:59' },
    { label: '2am to 2:59am', value: '02:00', startTime: '02:00', endTime: '02:59' },
    { label: '3am to 3:59am', value: '03:00', startTime: '03:00', endTime: '03:59' },
    { label: '4am to 4:59am', value: '04:00', startTime: '04:00', endTime: '04:59' },
    { label: '5am to 5:59am', value: '05:00', startTime: '05:00', endTime: '05:59' },
    { label: '6am to 6:59am', value: '06:00', startTime: '06:00', endTime: '06:59' },
    { label: '7am to 7:59am', value: '07:00', startTime: '07:00', endTime: '07:59' },
    { label: '8am to 8:59am', value: '08:00', startTime: '08:00', endTime: '08:59' },
    { label: '9am to 9:59am', value: '09:00', startTime: '09:00', endTime: '09:59' },
    { label: '10am to 10:59am', value: '10:00', startTime: '10:00', endTime: '10:59' },
    { label: '11am to 11:59am', value: '11:00', startTime: '11:00', endTime: '11:59' },
    { label: '12pm to 12:59pm', value: '12:00', startTime: '12:00', endTime: '12:59' },
    { label: '1pm to 1:59pm', value: '13:00', startTime: '13:00', endTime: '13:59' },
    { label: '2pm to 2:59pm', value: '14:00', startTime: '14:00', endTime: '14:59' },
    { label: '3pm to 3:59pm', value: '15:00', startTime: '15:00', endTime: '15:59' },
    { label: '4pm to 4:59pm', value: '16:00', startTime: '16:00', endTime: '16:59' },
    { label: '5pm to 5:59pm', value: '17:00', startTime: '17:00', endTime: '17:59' },
    { label: '6pm to 6:59pm', value: '18:00', startTime: '18:00', endTime: '18:59' },
    { label: '7pm to 7:59pm', value: '19:00', startTime: '19:00', endTime: '19:59' },
    { label: '8pm to 8:59pm', value: '20:00', startTime: '20:00', endTime: '20:59' },
    { label: '9pm to 9:59pm', value: '21:00', startTime: '21:00', endTime: '21:59' },
    { label: '10pm to 10:59pm', value: '22:00', startTime: '22:00', endTime: '22:59' },
    { label: '11pm to 11:59pm', value: '23:00', startTime: '23:00', endTime: '23:59' },
];

module.exports = { QUESTION_INPUT_TYPE, QUESTION_TYPE_OPTIONS, QUESTIONS_TEMPLATE_TYPE, TIME_TAB_MODE, TIME_RANGE_TYPE, DEFAULT_TIME_RANGES, HOURLY_TIME_RANGES };
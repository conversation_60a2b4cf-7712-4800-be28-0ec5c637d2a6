import CloseIcon from "../../../../../assets/svgs/close.svg";
import styles from "./AddPatient.module.scss";

const FutureDateConfirmation = ({ futureDateFields, onConfirm, onCancel }) => {
    return (
        <div className={styles.validationADT}>
            <div className={styles.validationADTOverlay}></div>
            <div className={styles.validationADTContent}>
                <div className={`df aic ${styles.hdr}`}>
                    <p className={`df aic ffmm fs16`}>Future Date Confirmation</p>
                    <div className={`mla`}>
                        <div className={`df acc ${styles.closeBtn}`} onClick={onCancel}>
                            <CloseIcon />
                        </div>
                    </div>
                </div>
                <div className={`p-t-8 p-r-20 p-b-20 p-l-20 df ffc ${styles.stepThree}`}>
                    <p style={{ marginBottom: '15px' }}>
                        The following dates are in the future:
                    </p>
                    <ul className={`m-t-10 ${styles.errorList}`}>
                        {futureDateFields.map((field, index) => (
                            <li key={index}>
                                <span>{field.label}: {field.date}</span>
                            </li>
                        ))}
                    </ul>
                    <div style={{ marginTop: '20px', display: 'flex', gap: '10px' }}>
                        <button
                            onClick={onConfirm}
                            style={{
                                padding: '10px 20px',
                                backgroundColor: '#4879F5',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: '600',
                                flex: 1
                            }}
                        >
                            Yes
                        </button>                        
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FutureDateConfirmation;

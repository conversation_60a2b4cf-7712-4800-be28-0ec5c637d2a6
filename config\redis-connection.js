
const IORedis = require('ioredis');

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV !== 'production';

const REDIS_CONNECTOR = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: process.env.REDIS_PORT || 6379,
  maxRetriesPerRequest: null, // ✅ Required for BullMQ - must be null!
  enableReadyCheck: false,    // ✅ Helps avoid unnecessary delays
  lazyConnect: true, // Don't connect immediately
  connectTimeout: isDevelopment ? 2000 : 5000, // Shorter timeout in development
  commandTimeout: isDevelopment ? 2000 : 5000, // Shorter timeout in development

  // Development-specific settings
  ...(isDevelopment ? {
    // In development: fail fast, don't retry
    enableOfflineQueue: false, // Don't queue commands when offline
    retryDelayOnFailover: 0,
    retryDelayOnClusterDown: 0,
    retryStrategy: (times) => {
      if (times === 1) {
        console.warn('⚠️  Redis connection failed in development mode');
        console.warn('🔧 Continuing without Redis features - start Redis server if needed');
      }
      return null; // Stop retrying immediately in development
    }
  } : {
    // In production: retry with backoff
    retryDelayOnFailover: 100,
    retryDelayOnClusterDown: 300,
    retryStrategy: (times) => {
      const delay = Math.min(times * 50, 2000);
      console.warn(`⚠️  Redis connection attempt ${times}, retrying in ${delay}ms`);
      return delay;
    }
  })
};

const REDIS_DEFAULT_REMOVE_CONFIG = {
  removeOnComplete: true,
  removeOnFail: {
    age: 24 * 3600,
  },
};

const createRedisConnection = () => {
  const redis = new IORedis(REDIS_CONNECTOR);

  // Handle connection errors gracefully
  redis.on('error', (error) => {
    if (error.code === 'ECONNREFUSED') {
      if (isDevelopment) {
        console.warn('⚠️  Redis not available in development - features requiring Redis will be disabled');
        console.warn('💡 To enable Redis features, start Redis server: redis-server');
      } else {
        console.error('❌ Redis connection refused in production - Redis server may not be running');
        console.error('🔧 Please start Redis server or check connection settings');
      }
    } else {
      console.warn('⚠️  Redis connection error:', error.message);
    }
    // Don't throw or crash - just log the warning
  });

  redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
  });

  redis.on('ready', () => {
    console.log('🚀 Redis ready for operations');
  });

  redis.on('close', () => {
    if (isDevelopment) {
      console.warn('⚠️  Redis connection closed in development');
    } else {
      console.warn('⚠️  Redis connection closed');
    }
  });

  redis.on('reconnecting', () => {
    if (!isDevelopment) {
      console.log('🔄 Redis reconnecting...');
    }
  });

  // In development, don't spam logs with connection attempts
  if (isDevelopment) {
    redis.on('end', () => {
      // Silently handle connection end in development
    });
  }

  return redis;
};


/**
 * Check if Redis is available without retrying in development
 */
const isRedisAvailable = async () => {
  try {
    const testConnection = createRedisConnection();
    await testConnection.connect();
    await testConnection.ping();
    await testConnection.quit();
    return true;
  } catch (error) {
    if (isDevelopment) {
      console.warn('⚠️  Redis not available in development mode');
      console.warn('💡 This is normal - Redis features will be disabled');
    } else {
      console.error('❌ Redis not available in production:', error.message);
    }
    return false;
  }
};

/**
 * Get Redis connection status for logging
 */
const getRedisStatus = () => {
  return {
    isDevelopment,
    host: REDIS_CONNECTOR.host,
    port: REDIS_CONNECTOR.port,
    retriesEnabled: !isDevelopment
  };
};

module.exports = {
  REDIS_CONNECTOR,
  REDIS_DEFAULT_REMOVE_CONFIG,
  createRedisConnection,
  isRedisAvailable,
  getRedisStatus,
  isDevelopment
};